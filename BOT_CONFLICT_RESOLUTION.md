# Bot Conflict Resolution System

## Overview

This document describes the comprehensive solution implemented to resolve Telegram bot conflicts, specifically the error:

```
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. 
Error code: 409. Description: Conflict: terminated by other getUpdates request; 
make sure that only one bot instance is running
```

## Problem Analysis

The error occurs when multiple instances of the same Telegram bot try to poll for updates simultaneously. This can happen due to:

1. **Multiple Process Instances**: Running the same bot script multiple times
2. **Zombie Processes**: Previous bot instances that didn't terminate properly
3. **Webhook Conflicts**: Existing webhooks interfering with polling
4. **Concurrent Polling**: Multiple threads trying to poll the same bot token

## Solution Components

### 1. Bot Instance Manager (`src/bot_instance.py`)

**Enhanced Features:**
- **PID File Management**: Uses process ID files to track running instances
- **File Locking**: Implements exclusive file locks to prevent multiple instances
- **Process Detection**: Automatically detects and handles conflicting processes
- **Graceful Cleanup**: Proper cleanup of resources on shutdown

**Key Functions:**
```python
class BotInstanceManager:
    def create_bot_instance(bot_type: str, token: str) -> Optional[telebot.TeleBot]
    def cleanup_all()
    def stop_bot(bot_type: str)
```

### 2. Bot Polling Manager (`src/utils/bot_polling_manager.py`)

**Enhanced Features:**
- **Conflict Detection**: Automatically detects API conflicts
- **Exponential Backoff**: Implements smart retry logic with increasing delays
- **Webhook Cleanup**: Automatically clears webhook state before polling
- **Graceful Recovery**: Handles errors and attempts recovery

**Key Functions:**
```python
class BotPollingManager:
    def start_polling() -> bool
    def stop_polling(timeout: int = 10) -> bool
    def _handle_conflict_error(error) -> bool
```

### 3. Error Handler (`src/utils/bot_error_handler.py`)

**Enhanced Features:**
- **Process Conflict Resolution**: Finds and terminates conflicting processes
- **Error Classification**: Categorizes different types of errors
- **Recovery Strategies**: Implements specific recovery actions
- **Statistics Tracking**: Monitors error patterns and frequencies

**Key Functions:**
```python
class BotErrorHandler:
    def handle_conflict_error(bot_name: str, error: Exception) -> bool
    def handle_api_error(bot_name: str, error: Exception) -> Dict[str, Any]
    def _resolve_process_conflict(bot_name: str) -> bool
```

### 4. Health Monitor (`src/utils/bot_health_monitor.py`)

**Enhanced Features:**
- **Real-time Monitoring**: Continuously monitors bot health
- **Automatic Recovery**: Triggers recovery actions when issues are detected
- **Performance Tracking**: Monitors CPU and memory usage
- **Heartbeat System**: Tracks bot responsiveness

**Key Functions:**
```python
class BotHealthMonitor:
    def register_bot(bot_name: str)
    def update_bot_heartbeat(bot_name: str)
    def report_bot_error(bot_name: str, error: str)
```

### 5. Enhanced Bot Runners (`src/utils/bot_runner_helper.py`)

**Enhanced Features:**
- **Standardized Error Handling**: Consistent error handling across all bots
- **Health Integration**: Integrated with health monitoring system
- **Conflict Prevention**: Built-in conflict prevention mechanisms
- **Recovery Callbacks**: Customizable recovery strategies

## Implementation Details

### Conflict Prevention Strategy

1. **PID File Locking**: Each bot type creates a unique PID file with exclusive lock
2. **Process Monitoring**: Continuously monitors for conflicting processes
3. **Webhook Management**: Automatically clears webhooks before starting polling
4. **Resource Cleanup**: Proper cleanup of all resources on shutdown

### Error Recovery Process

1. **Error Detection**: Identify the specific type of error (conflict, timeout, etc.)
2. **Process Analysis**: Check for conflicting processes using system tools
3. **Conflict Resolution**: Terminate conflicting processes if found
4. **State Cleanup**: Clear webhook state and reset polling configuration
5. **Retry Logic**: Implement exponential backoff for retry attempts
6. **Health Reporting**: Update health monitor with recovery status

### Health Monitoring

1. **Registration**: Each bot registers with the health monitor
2. **Heartbeat Updates**: Regular heartbeat updates indicate bot health
3. **Issue Detection**: Monitor for errors, conflicts, and performance issues
4. **Automatic Recovery**: Trigger recovery actions when thresholds are exceeded
5. **Statistics**: Track error patterns and recovery success rates

## Usage Instructions

### Running the Enhanced System

1. **Start with Enhanced Runners**:
   ```bash
   python main.py --bot user    # Uses enhanced user bot runner
   python main.py --bot admin   # Uses enhanced admin bot runner
   ```

2. **Test the System**:
   ```bash
   python test_bot_conflict_resolution.py
   ```

3. **Monitor Health**:
   ```python
   from src.utils.bot_health_monitor import get_health_monitor
   
   health_monitor = get_health_monitor()
   health_monitor.start_monitoring()
   summary = health_monitor.get_health_summary()
   ```

### Configuration Options

**Environment Variables:**
- `TEST_MODE=true`: Run in test mode without actual API connections
- `BOT_TOKEN`: Your bot token
- `ADMIN_BOT_TOKEN`: Admin bot token
- etc.

**Health Monitor Settings:**
```python
health_monitor.check_interval = 30  # Check every 30 seconds
health_monitor.max_error_count = 10  # Max errors before recovery
health_monitor.heartbeat_timeout = 120  # Heartbeat timeout in seconds
```

## Benefits

1. **Conflict Prevention**: Prevents multiple instances from running simultaneously
2. **Automatic Recovery**: Automatically recovers from conflicts and errors
3. **Better Monitoring**: Real-time health monitoring and statistics
4. **Improved Reliability**: More robust error handling and recovery
5. **Easy Debugging**: Comprehensive logging and error reporting
6. **Scalable Architecture**: Modular design for easy extension

## Testing

Run the test script to verify the system works correctly:

```bash
python test_bot_conflict_resolution.py
```

The test script will verify:
- Bot instance manager functionality
- Polling manager error handling
- Error handler conflict resolution
- Health monitor operation
- Enhanced bot runners

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure the application has permission to create lock files
2. **Port Conflicts**: Check if the configured ports are available
3. **Process Detection**: Verify psutil is installed for process monitoring
4. **File System**: Ensure the tmp/bot_locks directory can be created

### Debug Commands

```bash
# Check for running Python processes
ps aux | grep python

# Check for lock files
ls -la tmp/bot_locks/

# Monitor system resources
htop

# Check bot health
python -c "from src.utils.bot_health_monitor import get_health_monitor; print(get_health_monitor().get_health_summary())"
```

## Future Enhancements

1. **Web Dashboard**: Real-time monitoring dashboard
2. **Alert System**: Email/SMS alerts for critical issues
3. **Load Balancing**: Distribute load across multiple bot instances
4. **Database Integration**: Store health and error data in database
5. **API Endpoints**: REST API for monitoring and control

## Conclusion

This comprehensive solution addresses the Telegram bot conflict issue through multiple layers of protection:

- **Prevention**: PID files and process locking prevent conflicts
- **Detection**: Real-time monitoring detects issues quickly
- **Recovery**: Automatic recovery mechanisms resolve conflicts
- **Monitoring**: Health monitoring provides visibility into system status

The system is designed to be robust, scalable, and easy to maintain while providing excellent reliability for production deployments.
