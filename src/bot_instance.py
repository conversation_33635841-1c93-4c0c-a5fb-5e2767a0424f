"""
Bot instance initialization for the Wiz Aroma Food Delivery system.
This file initializes the bot instances to avoid circular imports and manages bot conflicts.
"""

import sys
import os
import time
import threading
import logging
import telebot
import telebot.apihelper
import hashlib
import fcntl
import signal
import atexit
from pathlib import Path
from typing import Dict, Optional, Set
from src.config import (
    BOT_TOKEN,
    ADMIN_BOT_TOKEN,
    FINANCE_BOT_TOKEN,
    MAINTENANCE_BOT_TOKEN,
    MANAGEMENT_BOT_TOKEN,
    RETRY_ON_ERROR,
    CONNECT_TIMEOUT,
    READ_TIMEOUT,
    TEST_MODE,
    logger,
)

# Configure API request timeouts - increase to avoid connection timeouts
telebot.apihelper.CONNECT_TIMEOUT = 15  # Increased connect timeout
telebot.apihelper.READ_TIMEOUT = 60  # Increased read timeout
telebot.apihelper.RETRY_ON_ERROR = True  # Enable retries
telebot.apihelper.ENABLE_MIDDLEWARE = (
    True  # Enable middleware for better error handling
)

# Set polling timeout to 60 seconds to reduce resource usage
POLLING_TIMEOUT = 60


class BotInstanceManager:
    """
    Centralized bot instance manager that prevents conflicts and manages bot lifecycle.
    Uses PID files and file locking to ensure only one instance of each bot type runs.
    """

    def __init__(self):
        self.bot_instances: Dict[str, telebot.TeleBot] = {}
        self.bot_locks: Dict[str, threading.Lock] = {}
        self.pid_files: Dict[str, Path] = {}
        self.lock_files: Dict[str, int] = {}  # File descriptors for lock files
        self.active_bots: Set[str] = set()
        self.shutdown_requested = False

        # Create locks directory
        self.locks_dir = Path("tmp/bot_locks")
        self.locks_dir.mkdir(parents=True, exist_ok=True)

        # Register cleanup on exit
        atexit.register(self.cleanup_all)
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_requested = True
        self.cleanup_all()
        sys.exit(0)

    def _create_pid_file(self, bot_type: str) -> bool:
        """
        Create a PID file for the bot type to track running instances.

        Args:
            bot_type: Type of bot (user, admin, finance, etc.)

        Returns:
            True if PID file created successfully, False if another instance exists
        """
        pid_file = self.locks_dir / f"{bot_type}_bot.pid"
        lock_file = self.locks_dir / f"{bot_type}_bot.lock"

        try:
            # Try to acquire exclusive lock
            lock_fd = os.open(str(lock_file), os.O_CREAT | os.O_WRONLY | os.O_TRUNC)
            fcntl.flock(lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)

            # Check if PID file exists and process is still running
            if pid_file.exists():
                try:
                    with open(pid_file, 'r') as f:
                        old_pid = int(f.read().strip())

                    # Check if process is still running
                    try:
                        os.kill(old_pid, 0)  # Signal 0 just checks if process exists
                        logger.error(f"Another {bot_type} bot instance is already running (PID: {old_pid})")
                        os.close(lock_fd)
                        return False
                    except OSError:
                        # Process doesn't exist, remove stale PID file
                        logger.info(f"Removing stale PID file for {bot_type} bot")
                        pid_file.unlink()
                except (ValueError, FileNotFoundError):
                    # Invalid or missing PID file, remove it
                    if pid_file.exists():
                        pid_file.unlink()

            # Write current PID
            current_pid = os.getpid()
            with open(pid_file, 'w') as f:
                f.write(str(current_pid))

            # Store references for cleanup
            self.pid_files[bot_type] = pid_file
            self.lock_files[bot_type] = lock_fd

            logger.info(f"Created PID file for {bot_type} bot (PID: {current_pid})")
            return True

        except (OSError, IOError) as e:
            if "Resource temporarily unavailable" in str(e) or "already locked" in str(e):
                logger.error(f"Another {bot_type} bot instance is already running")
            else:
                logger.error(f"Error creating PID file for {bot_type} bot: {e}")
            return False

    def _cleanup_bot_instance(self, bot_type: str):
        """Clean up PID file and lock for a specific bot type"""
        try:
            # Remove from active bots
            self.active_bots.discard(bot_type)

            # Close lock file
            if bot_type in self.lock_files:
                try:
                    os.close(self.lock_files[bot_type])
                except OSError:
                    pass
                del self.lock_files[bot_type]

            # Remove PID file
            if bot_type in self.pid_files:
                try:
                    self.pid_files[bot_type].unlink(missing_ok=True)
                except OSError:
                    pass
                del self.pid_files[bot_type]

            logger.info(f"Cleaned up {bot_type} bot instance")

        except Exception as e:
            logger.error(f"Error cleaning up {bot_type} bot instance: {e}")

    def cleanup_all(self):
        """Clean up all bot instances and files"""
        logger.info("Cleaning up all bot instances...")

        for bot_type in list(self.active_bots):
            self._cleanup_bot_instance(bot_type)

        # Clean up any remaining lock files
        try:
            for lock_fd in self.lock_files.values():
                try:
                    os.close(lock_fd)
                except OSError:
                    pass
            self.lock_files.clear()

            # Remove locks directory if empty
            if self.locks_dir.exists() and not any(self.locks_dir.iterdir()):
                self.locks_dir.rmdir()

        except Exception as e:
            logger.error(f"Error during final cleanup: {e}")

    def create_bot_instance(self, bot_type: str, token: str) -> Optional[telebot.TeleBot]:
        """
        Create a bot instance with conflict prevention.

        Args:
            bot_type: Type of bot (user, admin, finance, etc.)
            token: Bot token

        Returns:
            Bot instance if successful, None if conflict detected
        """
        if self.shutdown_requested:
            logger.warning(f"Shutdown requested, not creating {bot_type} bot")
            return None

        # Check for existing instance
        if not self._create_pid_file(bot_type):
            return None

        try:
            # Create bot instance
            bot = telebot.TeleBot(token)

            # Test connection if not in test mode
            if not TEST_MODE:
                try:
                    # Clear any existing webhook first
                    bot.remove_webhook()
                    time.sleep(1)

                    # Test connection
                    bot_info = bot.get_me()
                    logger.info(f"✅ {bot_type.title()} bot connected: @{bot_info.username}")

                except telebot.apihelper.ApiTelegramException as e:
                    if "Conflict: terminated by other getUpdates request" in str(e):
                        logger.error(f"❌ Conflict detected for {bot_type} bot - another instance is polling")
                        self._cleanup_bot_instance(bot_type)
                        return None
                    else:
                        logger.error(f"❌ API error for {bot_type} bot: {e}")
                        self._cleanup_bot_instance(bot_type)
                        return None

            # Store bot instance
            self.bot_instances[bot_type] = bot
            self.bot_locks[bot_type] = threading.Lock()
            self.active_bots.add(bot_type)

            logger.info(f"✅ {bot_type.title()} bot instance created successfully")
            return bot

        except Exception as e:
            logger.error(f"❌ Error creating {bot_type} bot instance: {e}")
            self._cleanup_bot_instance(bot_type)
            return None

    def get_bot_instance(self, bot_type: str) -> Optional[telebot.TeleBot]:
        """Get existing bot instance"""
        return self.bot_instances.get(bot_type)

    def is_bot_active(self, bot_type: str) -> bool:
        """Check if bot is active"""
        return bot_type in self.active_bots

    def stop_bot(self, bot_type: str):
        """Stop a specific bot instance"""
        if bot_type in self.bot_instances:
            try:
                bot = self.bot_instances[bot_type]
                if hasattr(bot, 'stop_polling'):
                    bot.stop_polling()
                logger.info(f"Stopped {bot_type} bot polling")
            except Exception as e:
                logger.error(f"Error stopping {bot_type} bot: {e}")
            finally:
                self._cleanup_bot_instance(bot_type)
                if bot_type in self.bot_instances:
                    del self.bot_instances[bot_type]
                if bot_type in self.bot_locks:
                    del self.bot_locks[bot_type]


# Create global bot instance manager
bot_manager = BotInstanceManager()

# Initialize bot instances using the manager
def initialize_bot_instances():
    """Initialize all bot instances using the bot manager"""
    global bot, admin_bot, finance_bot, maintenance_bot, management_bot

    try:
        if not TEST_MODE:
            # Normal operation with real tokens - create instances through manager
            bot = bot_manager.create_bot_instance("user", BOT_TOKEN)
            admin_bot = bot_manager.create_bot_instance("admin", ADMIN_BOT_TOKEN)
            finance_bot = bot_manager.create_bot_instance("finance", FINANCE_BOT_TOKEN)
            maintenance_bot = bot_manager.create_bot_instance("maintenance", MAINTENANCE_BOT_TOKEN)
            management_bot = bot_manager.create_bot_instance("management", MANAGEMENT_BOT_TOKEN)

            # Check if all bots were created successfully
            failed_bots = []
            if not bot:
                failed_bots.append("user")
            if not admin_bot:
                failed_bots.append("admin")
            if not finance_bot:
                failed_bots.append("finance")
            if not maintenance_bot:
                failed_bots.append("maintenance")
            if not management_bot:
                failed_bots.append("management")

            if failed_bots:
                logger.error(f"❌ Failed to create bot instances: {', '.join(failed_bots)}")
                logger.error("This usually means another instance is already running")
                return False

            logger.info("✅ All bot instances created successfully")
            return True

        else:
            # Test mode - create simple bot instances without conflict checking
            bot = telebot.TeleBot(BOT_TOKEN)
            admin_bot = telebot.TeleBot(ADMIN_BOT_TOKEN)
            finance_bot = telebot.TeleBot(FINANCE_BOT_TOKEN)
            maintenance_bot = telebot.TeleBot(MAINTENANCE_BOT_TOKEN)
            management_bot = telebot.TeleBot(MANAGEMENT_BOT_TOKEN)

            logger.info("Running in TEST MODE - bots initialized without API connection")
            logger.info("No Telegram functionality will be available")
            return True

    except telebot.apihelper.ApiException as e:
        if TEST_MODE:
            logger.warning(f"Telegram API error in test mode: {e}")
            logger.info("Continuing in test mode with limited functionality")
            return True
        else:
            logger.error(f"Telegram API error: {e}")
            logger.error("Please check your bot tokens in the .env file")
            logger.error(f"API Exception details: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            return False
    except Exception as e:
        if TEST_MODE:
            logger.warning(f"Error initializing bots in test mode: {e}")
            logger.info("Continuing in test mode with limited functionality")
            return True
        else:
            logger.error(f"Error initializing bots: {e}")
            return False


# Initialize bot instances (will be None if initialization fails)
bot = None
admin_bot = None
finance_bot = None
maintenance_bot = None
management_bot = None

# Try to initialize - this will be called by main.py when needed
def get_bot_instances():
    """Get bot instances, initializing them if needed"""
    global bot, admin_bot, finance_bot, maintenance_bot, management_bot

    if bot is None or admin_bot is None or finance_bot is None or maintenance_bot is None or management_bot is None:
        if not initialize_bot_instances():
            if not TEST_MODE:
                logger.error("Failed to initialize bot instances")
                return None, None, None, None, None

    return bot, admin_bot, finance_bot, maintenance_bot, management_bot

# Set longer session lifetime to reduce reconnections
telebot.apihelper.SESSION_TIME_TO_LIVE = 10 * 60  # 10 minutes session time to live
telebot.apihelper.MAX_RETRIES = 5  # Increase max retries

# Note: notification_bot functionality has been fully integrated into management_bot
# All notification features are now handled by the comprehensive management bot
