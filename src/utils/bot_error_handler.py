"""
Enhanced Error Handling for Telegram Bots
Provides comprehensive error handling, recovery mechanisms, and conflict resolution.
"""

import time
import threading
import logging
import os
import signal
import subprocess
import psutil
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path
from src.config import logger


class BotErrorHandler:
    """
    Comprehensive error handler for Telegram bot conflicts and issues.
    Provides automatic recovery, process management, and conflict resolution.
    """
    
    def __init__(self):
        self.conflict_handlers: Dict[str, Callable] = {}
        self.error_callbacks: Dict[str, List[Callable]] = {}
        self.recovery_strategies: Dict[str, Callable] = {}
        self.process_monitor = ProcessMonitor()
        
        # Error statistics
        self.error_stats: Dict[str, Dict[str, int]] = {}
        self.last_error_time: Dict[str, float] = {}
        
        # Recovery settings
        self.max_recovery_attempts = 3
        self.recovery_cooldown = 300  # 5 minutes
        
    def register_conflict_handler(self, bot_name: str, handler: Callable):
        """Register a conflict handler for a specific bot"""
        self.conflict_handlers[bot_name] = handler
        logger.info(f"Registered conflict handler for {bot_name} bot")
    
    def register_error_callback(self, error_type: str, callback: Callable):
        """Register an error callback for a specific error type"""
        if error_type not in self.error_callbacks:
            self.error_callbacks[error_type] = []
        self.error_callbacks[error_type].append(callback)
        logger.info(f"Registered error callback for {error_type}")
    
    def register_recovery_strategy(self, bot_name: str, strategy: Callable):
        """Register a recovery strategy for a specific bot"""
        self.recovery_strategies[bot_name] = strategy
        logger.info(f"Registered recovery strategy for {bot_name} bot")
    
    def handle_conflict_error(self, bot_name: str, error: Exception) -> bool:
        """
        Handle bot conflict errors with comprehensive recovery.
        
        Args:
            bot_name: Name of the bot experiencing conflict
            error: The conflict error
            
        Returns:
            True if recovery was successful, False otherwise
        """
        logger.error(f"🚨 Handling conflict for {bot_name} bot: {error}")
        
        # Update error statistics
        self._update_error_stats(bot_name, "conflict")
        
        # Check if we're in recovery cooldown
        if self._is_in_cooldown(bot_name):
            logger.warning(f"⏳ {bot_name} bot is in recovery cooldown")
            return False
        
        # Try to find and terminate conflicting processes
        if self._resolve_process_conflict(bot_name):
            logger.info(f"✅ Process conflict resolved for {bot_name} bot")
            
            # Wait for processes to fully terminate
            time.sleep(5)
            
            # Execute custom conflict handler if registered
            if bot_name in self.conflict_handlers:
                try:
                    return self.conflict_handlers[bot_name](error)
                except Exception as handler_error:
                    logger.error(f"❌ Conflict handler failed for {bot_name}: {handler_error}")
            
            return True
        
        logger.error(f"❌ Failed to resolve process conflict for {bot_name} bot")
        return False
    
    def handle_api_error(self, bot_name: str, error: Exception) -> Dict[str, Any]:
        """
        Handle API errors with appropriate recovery strategies.
        
        Args:
            bot_name: Name of the bot experiencing error
            error: The API error
            
        Returns:
            Dictionary with recovery information
        """
        error_type = type(error).__name__
        error_message = str(error)
        
        logger.error(f"🔧 Handling API error for {bot_name} bot: {error_type} - {error_message}")
        
        # Update error statistics
        self._update_error_stats(bot_name, error_type)
        
        # Determine recovery strategy based on error type
        recovery_info = {
            "bot_name": bot_name,
            "error_type": error_type,
            "error_message": error_message,
            "recovery_attempted": False,
            "recovery_successful": False,
            "recommended_action": "retry"
        }
        
        # Handle specific error types
        if "Conflict" in error_message:
            recovery_info["recovery_attempted"] = True
            recovery_info["recovery_successful"] = self.handle_conflict_error(bot_name, error)
            recovery_info["recommended_action"] = "restart" if not recovery_info["recovery_successful"] else "retry"
            
        elif "timeout" in error_message.lower():
            recovery_info["recommended_action"] = "retry_with_delay"
            
        elif "network" in error_message.lower() or "connection" in error_message.lower():
            recovery_info["recommended_action"] = "check_connection"
            
        elif "unauthorized" in error_message.lower() or "token" in error_message.lower():
            recovery_info["recommended_action"] = "check_token"
            
        # Execute error callbacks
        self._execute_error_callbacks(error_type, bot_name, error, recovery_info)
        
        return recovery_info
    
    def _resolve_process_conflict(self, bot_name: str) -> bool:
        """
        Find and terminate conflicting bot processes.
        
        Args:
            bot_name: Name of the bot to resolve conflicts for
            
        Returns:
            True if conflicts were resolved, False otherwise
        """
        try:
            logger.info(f"🔍 Searching for conflicting {bot_name} bot processes...")
            
            current_pid = os.getpid()
            conflicting_processes = []
            
            # Find Python processes that might be running the same bot
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'python' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline']
                        if cmdline and any('main.py' in arg for arg in cmdline):
                            # Check if it's running the same bot type
                            if any(f'--bot {bot_name}' in ' '.join(cmdline) or 
                                  f'--bot="{bot_name}"' in ' '.join(cmdline) or
                                  f'--bot={bot_name}' in ' '.join(cmdline) for _ in [1]):
                                if proc.info['pid'] != current_pid:
                                    conflicting_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if not conflicting_processes:
                logger.info(f"ℹ️ No conflicting processes found for {bot_name} bot")
                return True
            
            # Terminate conflicting processes
            logger.warning(f"⚠️ Found {len(conflicting_processes)} conflicting processes for {bot_name} bot")
            
            for proc in conflicting_processes:
                try:
                    logger.warning(f"🔪 Terminating conflicting process PID {proc.pid}")
                    proc.terminate()
                    
                    # Wait for graceful termination
                    try:
                        proc.wait(timeout=10)
                        logger.info(f"✅ Process {proc.pid} terminated gracefully")
                    except psutil.TimeoutExpired:
                        logger.warning(f"⚠️ Process {proc.pid} did not terminate gracefully, forcing kill")
                        proc.kill()
                        proc.wait(timeout=5)
                        logger.info(f"💀 Process {proc.pid} force killed")
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    logger.warning(f"⚠️ Could not terminate process {proc.pid}: {e}")
                except Exception as e:
                    logger.error(f"❌ Error terminating process {proc.pid}: {e}")
            
            logger.info(f"✅ Conflict resolution completed for {bot_name} bot")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error during conflict resolution for {bot_name} bot: {e}")
            return False
    
    def _update_error_stats(self, bot_name: str, error_type: str):
        """Update error statistics for monitoring"""
        if bot_name not in self.error_stats:
            self.error_stats[bot_name] = {}
        
        if error_type not in self.error_stats[bot_name]:
            self.error_stats[bot_name][error_type] = 0
        
        self.error_stats[bot_name][error_type] += 1
        self.last_error_time[f"{bot_name}_{error_type}"] = time.time()
    
    def _is_in_cooldown(self, bot_name: str) -> bool:
        """Check if bot is in recovery cooldown period"""
        last_error_key = f"{bot_name}_conflict"
        if last_error_key in self.last_error_time:
            time_since_last = time.time() - self.last_error_time[last_error_key]
            return time_since_last < self.recovery_cooldown
        return False
    
    def _execute_error_callbacks(self, error_type: str, bot_name: str, error: Exception, recovery_info: Dict[str, Any]):
        """Execute registered error callbacks"""
        if error_type in self.error_callbacks:
            for callback in self.error_callbacks[error_type]:
                try:
                    callback(bot_name, error, recovery_info)
                except Exception as callback_error:
                    logger.error(f"❌ Error in callback for {error_type}: {callback_error}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get comprehensive error statistics"""
        return {
            "error_stats": self.error_stats.copy(),
            "last_error_times": self.last_error_time.copy(),
            "active_handlers": list(self.conflict_handlers.keys()),
            "registered_callbacks": {k: len(v) for k, v in self.error_callbacks.items()}
        }
    
    def reset_error_stats(self, bot_name: Optional[str] = None):
        """Reset error statistics for a bot or all bots"""
        if bot_name:
            if bot_name in self.error_stats:
                del self.error_stats[bot_name]
            # Remove related last error times
            keys_to_remove = [k for k in self.last_error_time.keys() if k.startswith(f"{bot_name}_")]
            for key in keys_to_remove:
                del self.last_error_time[key]
            logger.info(f"Reset error statistics for {bot_name} bot")
        else:
            self.error_stats.clear()
            self.last_error_time.clear()
            logger.info("Reset all error statistics")


class ProcessMonitor:
    """Monitor and manage bot processes"""
    
    def __init__(self):
        self.monitored_processes: Dict[str, int] = {}
    
    def register_process(self, bot_name: str, pid: int):
        """Register a process for monitoring"""
        self.monitored_processes[bot_name] = pid
        logger.info(f"Registered process {pid} for {bot_name} bot")
    
    def is_process_running(self, bot_name: str) -> bool:
        """Check if a registered process is still running"""
        if bot_name not in self.monitored_processes:
            return False
        
        pid = self.monitored_processes[bot_name]
        try:
            proc = psutil.Process(pid)
            return proc.is_running()
        except psutil.NoSuchProcess:
            return False
    
    def get_process_info(self, bot_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a registered process"""
        if bot_name not in self.monitored_processes:
            return None
        
        pid = self.monitored_processes[bot_name]
        try:
            proc = psutil.Process(pid)
            return {
                "pid": pid,
                "name": proc.name(),
                "status": proc.status(),
                "cpu_percent": proc.cpu_percent(),
                "memory_info": proc.memory_info()._asdict(),
                "create_time": proc.create_time()
            }
        except psutil.NoSuchProcess:
            return None


# Global error handler instance
bot_error_handler = BotErrorHandler()


def get_error_handler() -> BotErrorHandler:
    """Get the global error handler instance"""
    return bot_error_handler
