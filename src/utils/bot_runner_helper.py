"""
Bot Runner Helper Functions
Provides standardized bot running functions with enhanced error handling.
"""

import time
import traceback
from typing import Optional
import telebot
from src.config import logger, TEST_MODE
from src.bot_instance import get_bot_instances
from src.utils.bot_polling_manager import create_polling_manager
from src.utils.bot_error_handler import get_error_handler
from src.utils.bot_health_monitor import get_health_monitor


def create_enhanced_bot_runner(bot_type: str, bot_name: str):
    """
    Create an enhanced bot runner function with standardized error handling.
    
    Args:
        bot_type: Type of bot (user, admin, finance, etc.)
        bot_name: Display name for the bot
        
    Returns:
        Function that runs the bot with enhanced error handling
    """
    
    def run_bot():
        """Run the bot with enhanced error handling and conflict resolution"""
        global shutdown_requested
        
        try:
            # Get bot instances
            bot, admin_bot, finance_bot, maintenance_bot, management_bot = get_bot_instances()
            
            # Select the appropriate bot instance
            bot_instance = None
            if bot_type == "user":
                bot_instance = bot
            elif bot_type == "admin":
                bot_instance = admin_bot
            elif bot_type == "finance":
                bot_instance = finance_bot
            elif bot_type == "maintenance":
                bot_instance = maintenance_bot
            elif bot_type == "management":
                bot_instance = management_bot
            
            if not bot_instance:
                logger.error(f"❌ Failed to get {bot_name} bot instance")
                return
            
            if TEST_MODE:
                logger.info(f"Running in TEST MODE - {bot_name} bot polling disabled")
                while not shutdown_requested:
                    time.sleep(1)
                return
            
            # Create polling manager with enhanced error handling
            polling_manager = create_polling_manager(bot_instance, bot_type)
            
            # Set up error handling callbacks
            error_handler = get_error_handler()
            health_monitor = get_health_monitor()

            # Register bot with health monitor
            health_monitor.register_bot(bot_type)

            def conflict_handler(bot_name_param: str, error: Exception) -> bool:
                logger.warning(f"🔧 Handling conflict for {bot_name_param} bot")
                health_monitor.report_bot_error(bot_name_param, str(error))
                return True  # Allow retry

            def error_callback(bot_name_param: str, error: Exception, recovery_info: dict):
                logger.info(f"📊 Error callback for {bot_name_param}: {recovery_info.get('recommended_action', 'unknown')}")
                health_monitor.report_bot_error(bot_name_param, str(error))

            def recovery_callback(bot_name_param: str, issues: List[str]):
                logger.info(f"🔄 Recovery triggered for {bot_name_param}: {', '.join(issues)}")
                # Could implement specific recovery actions here

            # Register handlers
            error_handler.register_conflict_handler(bot_type, conflict_handler)
            error_handler.register_error_callback("ApiTelegramException", error_callback)
            health_monitor.register_recovery_callback(bot_type, recovery_callback)
            
            # Set polling manager callbacks
            polling_manager.set_conflict_callback(lambda name, err: error_handler.handle_conflict_error(name, err))
            polling_manager.set_error_callback(lambda name, err: error_handler.handle_api_error(name, err))
            
            # Start polling
            logger.info(f"🚀 Starting {bot_name} bot with enhanced error handling...")
            if polling_manager.start_polling():
                logger.info(f"✅ {bot_name} bot polling started successfully")

                # Update health monitor
                health_monitor.update_bot_heartbeat(bot_type)

                # Keep running until shutdown requested
                heartbeat_counter = 0
                while not shutdown_requested and polling_manager.is_active():
                    time.sleep(1)
                    heartbeat_counter += 1

                    # Send heartbeat every 30 seconds
                    if heartbeat_counter >= 30:
                        health_monitor.update_bot_heartbeat(bot_type)
                        heartbeat_counter = 0

                # Stop polling gracefully
                polling_manager.stop_polling()
                logger.info(f"✅ {bot_name} bot stopped gracefully")
            else:
                logger.error(f"❌ Failed to start {bot_name} bot polling")
                health_monitor.report_bot_error(bot_type, "Failed to start polling")
                
        except Exception as e:
            logger.error(f"❌ Critical error in {bot_name} bot: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            logger.error(f"Traceback: {traceback.format_exc()}")
    
    return run_bot


def run_specialized_bot(bot_instance: telebot.TeleBot, bot_name: str):
    """
    Run a specialized bot (like order_track_bot or delivery_bot) with enhanced error handling.
    
    Args:
        bot_instance: The bot instance to run
        bot_name: Display name for the bot
    """
    global shutdown_requested
    
    try:
        if not bot_instance:
            logger.error(f"❌ {bot_name} bot instance not available")
            return
        
        if TEST_MODE:
            logger.info(f"Running in TEST MODE - {bot_name} bot polling disabled")
            while not shutdown_requested:
                time.sleep(1)
            return
        
        # Create polling manager
        polling_manager = create_polling_manager(bot_instance, bot_name.lower().replace(" ", "_"))
        
        # Set up error handling
        error_handler = get_error_handler()
        
        def conflict_handler(bot_name_param: str, error: Exception) -> bool:
            logger.warning(f"🔧 Handling conflict for {bot_name_param} bot")
            return True
        
        def error_callback(bot_name_param: str, error: Exception, recovery_info: dict):
            logger.info(f"📊 Error callback for {bot_name_param}: {recovery_info.get('recommended_action', 'unknown')}")
        
        # Register handlers
        bot_type = bot_name.lower().replace(" ", "_")
        error_handler.register_conflict_handler(bot_type, conflict_handler)
        error_handler.register_error_callback("ApiTelegramException", error_callback)
        
        # Set callbacks
        polling_manager.set_conflict_callback(lambda name, err: error_handler.handle_conflict_error(name, err))
        polling_manager.set_error_callback(lambda name, err: error_handler.handle_api_error(name, err))
        
        # Start polling
        logger.info(f"🚀 Starting {bot_name} bot with enhanced error handling...")
        if polling_manager.start_polling():
            logger.info(f"✅ {bot_name} bot polling started successfully")
            
            # Keep running until shutdown requested
            while not shutdown_requested and polling_manager.is_active():
                time.sleep(1)
            
            # Stop polling gracefully
            polling_manager.stop_polling()
            logger.info(f"✅ {bot_name} bot stopped gracefully")
        else:
            logger.error(f"❌ Failed to start {bot_name} bot polling")
            
    except Exception as e:
        logger.error(f"❌ Critical error in {bot_name} bot: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        logger.error(f"Traceback: {traceback.format_exc()}")


# Create standardized bot runner functions
run_user_bot_enhanced = create_enhanced_bot_runner("user", "User")
run_admin_bot_enhanced = create_enhanced_bot_runner("admin", "Admin")
run_finance_bot_enhanced = create_enhanced_bot_runner("finance", "Finance")
run_maintenance_bot_enhanced = create_enhanced_bot_runner("maintenance", "Maintenance")
run_management_bot_enhanced = create_enhanced_bot_runner("management", "Management")


def run_order_track_bot():
    """Run the order tracking bot with enhanced error handling"""
    try:
        from src.bots.order_track_bot import order_track_bot
        run_specialized_bot(order_track_bot, "Order Track")
    except ImportError as e:
        logger.warning(f"Could not import order tracking bot: {e}")
    except Exception as e:
        logger.error(f"Error running order tracking bot: {e}")


def run_delivery_bot():
    """Run the delivery bot with enhanced error handling"""
    try:
        from src.bots.delivery_bot import delivery_bot
        run_specialized_bot(delivery_bot, "Delivery")
    except ImportError as e:
        logger.warning(f"Could not import delivery bot: {e}")
    except Exception as e:
        logger.error(f"Error running delivery bot: {e}")
