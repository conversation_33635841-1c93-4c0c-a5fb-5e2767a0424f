"""
Bot Health Monitoring System
Monitors bot health, detects conflicts, and provides automatic recovery.
"""

import time
import threading
import logging
import psutil
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from src.config import logger


@dataclass
class BotHealthStatus:
    """Health status information for a bot"""
    bot_name: str
    is_running: bool
    last_heartbeat: datetime
    error_count: int
    last_error: Optional[str]
    uptime: timedelta
    memory_usage: float
    cpu_usage: float
    conflict_count: int
    recovery_attempts: int


class BotHealthMonitor:
    """
    Comprehensive health monitoring system for Telegram bots.
    Tracks bot status, detects issues, and triggers recovery actions.
    """
    
    def __init__(self, check_interval: int = 30):
        self.check_interval = check_interval
        self.bot_status: Dict[str, BotHealthStatus] = {}
        self.monitoring_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        self.is_monitoring = False
        
        # Health thresholds
        self.max_error_count = 10
        self.max_memory_usage = 500  # MB
        self.max_cpu_usage = 80  # Percentage
        self.heartbeat_timeout = 120  # Seconds
        self.max_recovery_attempts = 3
        
        # Callbacks
        self.health_callbacks: Dict[str, List[Callable]] = {}
        self.recovery_callbacks: Dict[str, Callable] = {}
        
        # Statistics
        self.total_checks = 0
        self.total_issues_detected = 0
        self.total_recoveries = 0
        
    def register_bot(self, bot_name: str):
        """Register a bot for health monitoring"""
        self.bot_status[bot_name] = BotHealthStatus(
            bot_name=bot_name,
            is_running=False,
            last_heartbeat=datetime.now(),
            error_count=0,
            last_error=None,
            uptime=timedelta(0),
            memory_usage=0.0,
            cpu_usage=0.0,
            conflict_count=0,
            recovery_attempts=0
        )
        logger.info(f"Registered {bot_name} bot for health monitoring")
    
    def update_bot_heartbeat(self, bot_name: str):
        """Update bot heartbeat to indicate it's alive"""
        if bot_name in self.bot_status:
            self.bot_status[bot_name].last_heartbeat = datetime.now()
            self.bot_status[bot_name].is_running = True
    
    def report_bot_error(self, bot_name: str, error: str):
        """Report an error for a bot"""
        if bot_name in self.bot_status:
            status = self.bot_status[bot_name]
            status.error_count += 1
            status.last_error = error
            
            # Check if error is a conflict
            if "Conflict" in error or "getUpdates" in error:
                status.conflict_count += 1
                logger.warning(f"Conflict detected for {bot_name} bot (total: {status.conflict_count})")
            
            logger.warning(f"Error reported for {bot_name} bot: {error}")
    
    def report_bot_recovery(self, bot_name: str):
        """Report successful recovery for a bot"""
        if bot_name in self.bot_status:
            status = self.bot_status[bot_name]
            status.recovery_attempts += 1
            status.error_count = max(0, status.error_count - 1)  # Reduce error count on recovery
            self.total_recoveries += 1
            logger.info(f"Recovery reported for {bot_name} bot (attempt {status.recovery_attempts})")
    
    def register_health_callback(self, event_type: str, callback: Callable):
        """Register a callback for health events"""
        if event_type not in self.health_callbacks:
            self.health_callbacks[event_type] = []
        self.health_callbacks[event_type].append(callback)
        logger.info(f"Registered health callback for {event_type}")
    
    def register_recovery_callback(self, bot_name: str, callback: Callable):
        """Register a recovery callback for a specific bot"""
        self.recovery_callbacks[bot_name] = callback
        logger.info(f"Registered recovery callback for {bot_name} bot")
    
    def _check_bot_health(self, bot_name: str) -> List[str]:
        """
        Check health of a specific bot and return list of issues.
        
        Args:
            bot_name: Name of the bot to check
            
        Returns:
            List of health issues detected
        """
        issues = []
        status = self.bot_status[bot_name]
        
        # Check heartbeat timeout
        time_since_heartbeat = datetime.now() - status.last_heartbeat
        if time_since_heartbeat.total_seconds() > self.heartbeat_timeout:
            issues.append(f"Heartbeat timeout ({time_since_heartbeat.total_seconds():.1f}s)")
            status.is_running = False
        
        # Check error count
        if status.error_count > self.max_error_count:
            issues.append(f"High error count ({status.error_count})")
        
        # Check memory usage
        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            status.memory_usage = memory_mb
            
            if memory_mb > self.max_memory_usage:
                issues.append(f"High memory usage ({memory_mb:.1f}MB)")
        except psutil.NoSuchProcess:
            issues.append("Process not found")
        
        # Check CPU usage
        try:
            cpu_percent = psutil.Process().cpu_percent()
            status.cpu_usage = cpu_percent
            
            if cpu_percent > self.max_cpu_usage:
                issues.append(f"High CPU usage ({cpu_percent:.1f}%)")
        except psutil.NoSuchProcess:
            pass
        
        # Check conflict count
        if status.conflict_count > 3:
            issues.append(f"Multiple conflicts detected ({status.conflict_count})")
        
        # Check recovery attempts
        if status.recovery_attempts > self.max_recovery_attempts:
            issues.append(f"Too many recovery attempts ({status.recovery_attempts})")
        
        return issues
    
    def _trigger_recovery(self, bot_name: str, issues: List[str]):
        """Trigger recovery actions for a bot"""
        logger.warning(f"Triggering recovery for {bot_name} bot due to: {', '.join(issues)}")
        
        # Execute recovery callback if registered
        if bot_name in self.recovery_callbacks:
            try:
                self.recovery_callbacks[bot_name](bot_name, issues)
                self.report_bot_recovery(bot_name)
            except Exception as e:
                logger.error(f"Recovery callback failed for {bot_name} bot: {e}")
        
        # Execute health callbacks
        if "recovery_needed" in self.health_callbacks:
            for callback in self.health_callbacks["recovery_needed"]:
                try:
                    callback(bot_name, issues)
                except Exception as e:
                    logger.error(f"Health callback failed: {e}")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        logger.info("Bot health monitoring started")
        
        while not self.stop_event.is_set():
            try:
                self.total_checks += 1
                issues_found = False
                
                for bot_name in self.bot_status.keys():
                    issues = self._check_bot_health(bot_name)
                    
                    if issues:
                        issues_found = True
                        self.total_issues_detected += len(issues)
                        logger.warning(f"Health issues detected for {bot_name} bot: {', '.join(issues)}")
                        
                        # Trigger recovery if needed
                        status = self.bot_status[bot_name]
                        if (status.error_count > 5 or 
                            status.conflict_count > 2 or 
                            not status.is_running):
                            self._trigger_recovery(bot_name, issues)
                
                if not issues_found and self.total_checks % 10 == 0:  # Log every 10th check if no issues
                    logger.debug(f"Health check #{self.total_checks}: All bots healthy")
                
                # Wait for next check
                self.stop_event.wait(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
                self.stop_event.wait(5)  # Wait a bit before retrying
        
        logger.info("Bot health monitoring stopped")
    
    def start_monitoring(self):
        """Start health monitoring"""
        if self.is_monitoring:
            logger.warning("Health monitoring is already running")
            return
        
        self.stop_event.clear()
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            name="bot_health_monitor",
            daemon=True
        )
        self.monitoring_thread.start()
        self.is_monitoring = True
        logger.info("Bot health monitoring started")
    
    def stop_monitoring(self):
        """Stop health monitoring"""
        if not self.is_monitoring:
            return
        
        self.stop_event.set()
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=10)
        
        self.is_monitoring = False
        logger.info("Bot health monitoring stopped")
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get comprehensive health summary"""
        summary = {
            "monitoring_active": self.is_monitoring,
            "total_checks": self.total_checks,
            "total_issues_detected": self.total_issues_detected,
            "total_recoveries": self.total_recoveries,
            "bot_count": len(self.bot_status),
            "bots": {}
        }
        
        for bot_name, status in self.bot_status.items():
            summary["bots"][bot_name] = {
                "is_running": status.is_running,
                "error_count": status.error_count,
                "conflict_count": status.conflict_count,
                "recovery_attempts": status.recovery_attempts,
                "memory_usage_mb": status.memory_usage,
                "cpu_usage_percent": status.cpu_usage,
                "last_heartbeat": status.last_heartbeat.isoformat(),
                "last_error": status.last_error
            }
        
        return summary
    
    def reset_bot_stats(self, bot_name: str):
        """Reset statistics for a specific bot"""
        if bot_name in self.bot_status:
            status = self.bot_status[bot_name]
            status.error_count = 0
            status.conflict_count = 0
            status.recovery_attempts = 0
            status.last_error = None
            logger.info(f"Reset statistics for {bot_name} bot")


# Global health monitor instance
bot_health_monitor = BotHealthMonitor()


def get_health_monitor() -> BotHealthMonitor:
    """Get the global health monitor instance"""
    return bot_health_monitor
