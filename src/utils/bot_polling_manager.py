"""
Bot Polling Manager for Wiz Aroma Food Delivery System
Handles bot polling with conflict resolution, error handling, and recovery mechanisms.
"""

import time
import threading
import logging
import telebot
import telebot.apihelper
from typing import Optional, Callable, Dict, Any
from src.config import logger


class BotPollingManager:
    """
    Manages bot polling with enhanced error handling and conflict resolution.
    Provides automatic recovery from API conflicts and connection issues.
    """
    
    def __init__(self, bot: telebot.TeleBot, bot_name: str):
        self.bot = bot
        self.bot_name = bot_name
        self.is_polling = False
        self.polling_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        self.retry_count = 0
        self.max_retries = 5
        self.base_retry_delay = 5  # Base delay in seconds
        self.max_retry_delay = 60  # Maximum delay in seconds
        
        # Polling configuration
        self.polling_timeout = 60
        self.long_polling_timeout = 60
        self.none_stop = True
        self.interval = 1
        
        # Error handling callbacks
        self.on_conflict_callback: Optional[Callable] = None
        self.on_error_callback: Optional[Callable] = None
        self.on_recovery_callback: Optional[Callable] = None
    
    def set_conflict_callback(self, callback: Callable):
        """Set callback for handling conflicts"""
        self.on_conflict_callback = callback
    
    def set_error_callback(self, callback: Callable):
        """Set callback for handling general errors"""
        self.on_error_callback = callback
    
    def set_recovery_callback(self, callback: Callable):
        """Set callback for successful recovery"""
        self.on_recovery_callback = callback
    
    def _clear_webhook_state(self) -> bool:
        """
        Clear any existing webhook state to prevent conflicts.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Clearing webhook state for {self.bot_name} bot...")
            self.bot.remove_webhook()
            time.sleep(2)  # Give time for webhook to be fully removed
            logger.info(f"Webhook state cleared for {self.bot_name} bot")
            return True
        except Exception as e:
            logger.error(f"Error clearing webhook state for {self.bot_name} bot: {e}")
            return False
    
    def _calculate_retry_delay(self) -> int:
        """
        Calculate exponential backoff delay for retries.
        
        Returns:
            Delay in seconds
        """
        delay = min(self.base_retry_delay * (2 ** self.retry_count), self.max_retry_delay)
        return delay
    
    def _handle_conflict_error(self, error: telebot.apihelper.ApiTelegramException) -> bool:
        """
        Handle API conflict errors with specific recovery strategies.
        
        Args:
            error: The API exception that occurred
            
        Returns:
            True if recovery should be attempted, False to stop
        """
        logger.error(f"🚨 Bot conflict detected for {self.bot_name} bot!")
        logger.error(f"Error: {error}")
        
        if self.on_conflict_callback:
            try:
                self.on_conflict_callback(self.bot_name, error)
            except Exception as callback_error:
                logger.error(f"Error in conflict callback: {callback_error}")
        
        # Increment retry count
        self.retry_count += 1
        
        if self.retry_count >= self.max_retries:
            logger.error(f"❌ Max retries ({self.max_retries}) reached for {self.bot_name} bot")
            logger.error("Another instance is likely running. Please check and stop other instances.")
            return False
        
        # Calculate delay and wait
        delay = self._calculate_retry_delay()
        logger.info(f"⏳ Waiting {delay} seconds before retry {self.retry_count}/{self.max_retries}")
        
        # Use interruptible sleep
        for _ in range(delay):
            if self.stop_event.is_set():
                return False
            time.sleep(1)
        
        # Clear webhook state before retry
        if not self._clear_webhook_state():
            logger.error(f"Failed to clear webhook state for {self.bot_name} bot")
            return False
        
        logger.info(f"🔄 Retrying {self.bot_name} bot polling (attempt {self.retry_count}/{self.max_retries})")
        return True
    
    def _handle_general_error(self, error: Exception) -> bool:
        """
        Handle general errors during polling.
        
        Args:
            error: The exception that occurred
            
        Returns:
            True if recovery should be attempted, False to stop
        """
        logger.error(f"❌ Error in {self.bot_name} bot polling: {error}")
        logger.error(f"Exception type: {type(error).__name__}")
        
        if self.on_error_callback:
            try:
                self.on_error_callback(self.bot_name, error)
            except Exception as callback_error:
                logger.error(f"Error in error callback: {callback_error}")
        
        # For general errors, wait a bit and retry
        self.retry_count += 1
        
        if self.retry_count >= self.max_retries:
            logger.error(f"❌ Max retries ({self.max_retries}) reached for {self.bot_name} bot")
            return False
        
        delay = min(10, self.retry_count * 2)  # Shorter delay for general errors
        logger.info(f"⏳ Waiting {delay} seconds before retry {self.retry_count}/{self.max_retries}")
        
        for _ in range(delay):
            if self.stop_event.is_set():
                return False
            time.sleep(1)
        
        return True
    
    def _polling_loop(self):
        """Main polling loop with error handling"""
        logger.info(f"🚀 Starting {self.bot_name} bot polling...")
        
        while not self.stop_event.is_set():
            try:
                # Clear webhook state before starting
                if not self._clear_webhook_state():
                    logger.error(f"Failed to clear webhook state for {self.bot_name} bot")
                    break
                
                # Start polling
                logger.info(f"📡 {self.bot_name} bot is now polling for updates...")
                self.bot.infinity_polling(
                    timeout=self.polling_timeout,
                    long_polling_timeout=self.long_polling_timeout,
                    none_stop=self.none_stop,
                    interval=self.interval
                )
                
                # If we reach here, polling stopped normally
                logger.info(f"✅ {self.bot_name} bot polling stopped normally")
                break
                
            except telebot.apihelper.ApiTelegramException as e:
                if "Conflict: terminated by other getUpdates request" in str(e):
                    if not self._handle_conflict_error(e):
                        break
                else:
                    logger.error(f"❌ API error in {self.bot_name} bot: {e}")
                    if not self._handle_general_error(e):
                        break
                        
            except Exception as e:
                if not self._handle_general_error(e):
                    break
            
            # Check if we should continue
            if self.stop_event.is_set():
                break
        
        # Reset polling state
        self.is_polling = False
        logger.info(f"🛑 {self.bot_name} bot polling loop ended")
    
    def start_polling(self) -> bool:
        """
        Start bot polling in a separate thread.
        
        Returns:
            True if polling started successfully, False otherwise
        """
        if self.is_polling:
            logger.warning(f"⚠️ {self.bot_name} bot is already polling")
            return True
        
        try:
            # Reset state
            self.stop_event.clear()
            self.retry_count = 0
            
            # Start polling thread
            self.polling_thread = threading.Thread(
                target=self._polling_loop,
                name=f"{self.bot_name}_polling_thread",
                daemon=True
            )
            self.polling_thread.start()
            self.is_polling = True
            
            logger.info(f"✅ {self.bot_name} bot polling started successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start {self.bot_name} bot polling: {e}")
            return False
    
    def stop_polling(self, timeout: int = 10) -> bool:
        """
        Stop bot polling gracefully.
        
        Args:
            timeout: Maximum time to wait for polling to stop
            
        Returns:
            True if stopped successfully, False otherwise
        """
        if not self.is_polling:
            logger.info(f"ℹ️ {self.bot_name} bot is not currently polling")
            return True
        
        try:
            logger.info(f"🛑 Stopping {self.bot_name} bot polling...")
            
            # Signal stop
            self.stop_event.set()
            
            # Stop bot polling
            if hasattr(self.bot, 'stop_polling'):
                self.bot.stop_polling()
            
            # Wait for thread to finish
            if self.polling_thread and self.polling_thread.is_alive():
                self.polling_thread.join(timeout=timeout)
                
                if self.polling_thread.is_alive():
                    logger.warning(f"⚠️ {self.bot_name} bot polling thread did not stop within {timeout} seconds")
                    return False
            
            self.is_polling = False
            logger.info(f"✅ {self.bot_name} bot polling stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error stopping {self.bot_name} bot polling: {e}")
            return False
    
    def is_active(self) -> bool:
        """Check if polling is currently active"""
        return self.is_polling and not self.stop_event.is_set()
    
    def get_status(self) -> Dict[str, Any]:
        """Get current polling status"""
        return {
            "bot_name": self.bot_name,
            "is_polling": self.is_polling,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "thread_alive": self.polling_thread.is_alive() if self.polling_thread else False
        }


def create_polling_manager(bot: telebot.TeleBot, bot_name: str) -> BotPollingManager:
    """
    Create a polling manager for a bot.
    
    Args:
        bot: The bot instance
        bot_name: Name of the bot for logging
        
    Returns:
        BotPollingManager instance
    """
    return BotPollingManager(bot, bot_name)
